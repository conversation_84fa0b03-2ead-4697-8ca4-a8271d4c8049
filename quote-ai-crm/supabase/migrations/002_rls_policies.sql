-- Enable RLS on all tables
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_log ENABLE ROW LEVEL SECURITY;

-- Companies policies
CREATE POLICY "Users can view their own company" ON companies
  FOR SELECT USING (auth.uid() IN (
    SELECT user_id FROM auth.users WHERE email = auth.jwt() ->> 'email'
  ));

CREATE POLICY "Users can update their own company" ON companies
  FOR UPDATE USING (auth.uid() IN (
    SELECT user_id FROM auth.users WHERE email = auth.jwt() ->> 'email'
  ));

-- Users policies
CREATE POLICY "Users can view users in their company" ON users
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (email = auth.jwt() ->> 'email');

CREATE POLICY "Users can insert their own profile" ON users
  FOR INSERT WITH CHECK (email = auth.jwt() ->> 'email');

-- Customers policies
CREATE POLICY "Users can view customers in their company" ON customers
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can insert customers in their company" ON customers
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can update customers in their company" ON customers
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can delete customers in their company" ON customers
  FOR DELETE USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

-- Projects policies
CREATE POLICY "Users can view projects in their company" ON projects
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can insert projects in their company" ON projects
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can update projects in their company" ON projects
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can delete projects in their company" ON projects
  FOR DELETE USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

-- Quotes policies
CREATE POLICY "Users can view quotes in their company" ON quotes
  FOR SELECT USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can insert quotes in their company" ON quotes
  FOR INSERT WITH CHECK (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can update quotes in their company" ON quotes
  FOR UPDATE USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can delete quotes in their company" ON quotes
  FOR DELETE USING (
    project_id IN (
      SELECT p.id FROM projects p
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

-- Quote items policies
CREATE POLICY "Users can view quote items in their company" ON quote_items
  FOR SELECT USING (
    quote_id IN (
      SELECT q.id FROM quotes q
      JOIN projects p ON q.project_id = p.id
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can insert quote items in their company" ON quote_items
  FOR INSERT WITH CHECK (
    quote_id IN (
      SELECT q.id FROM quotes q
      JOIN projects p ON q.project_id = p.id
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can update quote items in their company" ON quote_items
  FOR UPDATE USING (
    quote_id IN (
      SELECT q.id FROM quotes q
      JOIN projects p ON q.project_id = p.id
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can delete quote items in their company" ON quote_items
  FOR DELETE USING (
    quote_id IN (
      SELECT q.id FROM quotes q
      JOIN projects p ON q.project_id = p.id
      JOIN users u ON p.company_id = u.company_id
      WHERE u.email = auth.jwt() ->> 'email'
    )
  );

-- Invoices policies
CREATE POLICY "Users can view invoices in their company" ON invoices
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can insert invoices in their company" ON invoices
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can update invoices in their company" ON invoices
  FOR UPDATE USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can delete invoices in their company" ON invoices
  FOR DELETE USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

-- Activity log policies
CREATE POLICY "Users can view activity log in their company" ON activity_log
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  );

CREATE POLICY "Users can insert activity log in their company" ON activity_log
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM users WHERE email = auth.jwt() ->> 'email'
    )
  ); 