/* Holographic Design System */
:root {
  /* Holographic Colors */
  --holo-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --holo-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --holo-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --holo-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  
  /* Glass Effect Colors */
  --glass-light: rgba(255, 255, 255, 0.1);
  --glass-dark: rgba(0, 0, 0, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  
  /* Glow Effects */
  --glow-primary: 0 0 20px rgba(102, 126, 234, 0.5);
  --glow-secondary: 0 0 20px rgba(245, 87, 108, 0.5);
  --glow-accent: 0 0 20px rgba(0, 242, 254, 0.5);
}

/* Custom Keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 0.8; }
}

@keyframes glow {
  0%, 100% { box-shadow: var(--glow-primary); }
  50% { box-shadow: var(--glow-secondary); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  overflow-x: hidden;
}

/* Container Utilities */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Grid Utilities */
.grid {
  display: grid;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.flex {
  display: flex;
}

.gap-4 { gap: 1rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }

.space-y-8 > * + * {
  margin-top: 2rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-white { color: white; }
.text-5xl { font-size: 3rem; }
.text-7xl { font-size: 4.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-xl { font-size: 1.25rem; }
.text-lg { font-size: 1.125rem; }
.text-sm { font-size: 0.875rem; }
.font-bold { font-weight: bold; }
.font-semibold { font-weight: 600; }

/* Responsive */
@media (min-width: 1024px) {
  .lg\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .lg\\:text-left {
    text-align: left;
  }
  
  .lg\\:justify-start {
    justify-content: flex-start;
  }
  
  .lg\\:justify-end {
    justify-content: flex-end;
  }
}

@media (min-width: 640px) {
  .sm\\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .sm\\:flex-row {
    flex-direction: row;
  }
}

@media (min-width: 768px) {
  .md\\:text-7xl { font-size: 4.5rem; }
  .md\\:text-4xl { font-size: 2.25rem; }
}

/* Utility Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* Holographic Text Effect */
.holographic-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 8s ease infinite;
  font-weight: bold;
  text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

/* Glass Morphism */
.glass {
  background: var(--glass-light);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-dark {
  background: var(--glass-dark);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* Button Styles */
.btn-primary {
  padding: 0.75rem 1.5rem;
  background: var(--holo-primary);
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--glow-primary);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-secondary {
  padding: 0.75rem 1.5rem;
  background: var(--glass-light);
  backdrop-filter: blur(20px);
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  border: 1px solid var(--glass-border);
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Card Styles */
.card-glass {
  background: var(--glass-light);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.card-holographic {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(245, 87, 108, 0.2));
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Glowing Elements */
.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

/* Input Styles */
.input-glass {
  background: var(--glass-light);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.input-glass:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.input-glass::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Loading Animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
