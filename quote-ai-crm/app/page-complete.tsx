import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON>, TrendingUp, Clock, Star, CheckCircle, Zap, Shield, BarChart3, Brain, Rocket, Mail } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Animated background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 animate-pulse-slow"></div>
          <div className="absolute top-0 left-0 w-72 h-72 bg-primary-500/30 rounded-full blur-3xl animate-float"></div>
          <div className="absolute top-0 right-0 w-72 h-72 bg-secondary-500/30 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-accent-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Value Proposition */}
            <div className="text-center lg:text-left">
              <div className="inline-flex items-center gap-2 bg-black/30 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-8">
                <Sparkles className="w-4 h-4 text-primary-400" />
                <span className="text-white/80 text-sm">Powered by AI</span>
              </div>

              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
                Générez des devis <span className="holographic-text">intelligents</span> en quelques secondes
              </h1>
              
              <p className="text-xl text-white/70 mb-8 leading-relaxed">
                La seule plateforme CRM qui combine intelligence artificielle et gestion commerciale pour transformer vos visites en devis gagnants automatiquement.
              </p>

              {/* Benefits List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-white/80">Devis en 30 secondes</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-white/80">+40% de taux de conversion</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-white/80">IA adaptée à votre métier</span>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-white/80">CRM intégré complet</span>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <button className="button-primary text-lg px-8 py-4">
                  <Rocket className="w-5 h-5" />
                  Essai gratuit 14 jours
                </button>
                <button className="button-secondary text-lg px-8 py-4">
                  <ArrowRight className="w-5 h-5" />
                  Voir une démo
                </button>
              </div>

              {/* Trust indicators */}
              <div className="flex items-center justify-center lg:justify-start gap-6 text-sm text-white/60">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Aucune carte bancaire requise</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4 text-green-400" />
                  <span>SSL sécurisé & RGPD</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-green-400" />
                  <span>Support 7j/7</span>
                </div>
              </div>
            </div>

            {/* Right side - Login Form */}
            <div className="card-holographic max-w-md mx-auto lg:mx-0">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">Connexion rapide</h3>
                <p className="text-white/70">Accédez à votre dashboard</p>
              </div>

              <div className="space-y-4 mb-6">
                <button className="w-full flex items-center justify-center gap-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg px-4 py-3 text-white transition-colors">
                  <Sparkles className="w-5 h-5" />
                  Continuer avec Apple
                </button>
                <button className="w-full flex items-center justify-center gap-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg px-4 py-3 text-white transition-colors">
                  <Zap className="w-5 h-5" />
                  Continuer avec Google
                </button>
              </div>

              <div className="relative mb-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-white/20"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-transparent text-white/70">ou</span>
                </div>
              </div>

              <form className="space-y-4">
                <input
                  type="email"
                  placeholder="Votre email"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <input
                  type="password"
                  placeholder="Mot de passe"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
                <button className="w-full button-primary">
                  Se connecter
                  <ArrowRight className="w-4 h-4" />
                </button>
              </form>

              <p className="text-center text-white/60 text-sm mt-4">
                Pas encore de compte ? <a href="#" className="text-primary-400 hover:text-primary-300">S'inscrire</a>
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 py-20 bg-black/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Tout ce dont vous avez besoin pour <span className="holographic-text">réussir</span>
            </h2>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              Une suite complète d'outils d'IA et de CRM conçue spécifiquement pour les artisans modernes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="card-holographic group hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-400 rounded-lg flex items-center justify-center mb-6 group-hover:animate-glow">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">IA Génération de Devis</h3>
              <p className="text-white/70 mb-4">
                Notre IA analyse vos photos de chantier et génère automatiquement des devis précis en 30 secondes
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Reconnaissance d'images</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Calcul automatique</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Base de prix actualisée</span>
                </li>
              </ul>
            </div>

            {/* Feature 2 */}
            <div className="card-holographic group hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-secondary-500 to-secondary-400 rounded-lg flex items-center justify-center mb-6 group-hover:animate-glow">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">CRM Intelligent</h3>
              <p className="text-white/70 mb-4">
                Gérez vos clients, prospects et projets avec un CRM conçu pour les artisans
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Suivi automatique</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Pipeline de vente</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Relances intelligentes</span>
                </li>
              </ul>
            </div>

            {/* Feature 3 */}
            <div className="card-holographic group hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-accent-500 to-accent-400 rounded-lg flex items-center justify-center mb-6 group-hover:animate-glow">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Analytics Avancés</h3>
              <p className="text-white/70 mb-4">
                Analysez vos performances et optimisez votre business avec des insights précis
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Taux de conversion</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">ROI par projet</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Prédictions IA</span>
                </li>
              </ul>
            </div>

            {/* Feature 4 */}
            <div className="card-holographic group hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-400 rounded-lg flex items-center justify-center mb-6 group-hover:animate-glow">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Automation</h3>
              <p className="text-white/70 mb-4">
                Automatisez vos tâches répétitives et concentrez-vous sur ce qui compte vraiment
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Envoi automatique</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Relances programmées</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Facturation auto</span>
                </li>
              </ul>
            </div>

            {/* Feature 5 */}
            <div className="card-holographic group hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-400 rounded-lg flex items-center justify-center mb-6 group-hover:animate-glow">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Sécurité & Conformité</h3>
              <p className="text-white/70 mb-4">
                Vos données sont protégées avec les plus hauts standards de sécurité
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Chiffrement SSL</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">RGPD conforme</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Sauvegarde auto</span>
                </li>
              </ul>
            </div>

            {/* Feature 6 */}
            <div className="card-holographic group hover:scale-105 transition-all duration-300">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-400 rounded-lg flex items-center justify-center mb-6 group-hover:animate-glow">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Croissance Business</h3>
              <p className="text-white/70 mb-4">
                Développez votre activité avec des outils pensés pour la croissance
              </p>
              <ul className="space-y-2">
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Lead scoring IA</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Recommandations</span>
                </li>
                <li className="flex items-center gap-2 text-white/80">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Optimisation prix</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <div className="card-holographic max-w-4xl mx-auto text-center relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10"></div>
            <div className="relative z-10">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Prêt à transformer votre business ?
              </h2>
              <p className="text-xl text-white/80 mb-8">
                Rejoignez des centaines d'artisans qui ont déjà révolutionné leur façon de travailler
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button className="button-primary text-lg px-8 py-4">
                  <Rocket className="w-5 h-5 mr-2" />
                  Commencer gratuitement
                </button>
                <button className="button-secondary text-lg px-8 py-4">
                  Voir une démo
                </button>
              </div>

              <div className="flex justify-center items-center gap-6 text-sm text-white/60">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Essai gratuit 14 jours</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Sans carte bancaire</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span>Support inclus</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
