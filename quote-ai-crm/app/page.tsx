import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Users, T<PERSON>ding<PERSON>p, Clock, Star, CheckCircle, Zap, Shield, BarChart3, Brain, Rocket, Mail, Lock } from 'lucide-react';
import { LoginForm } from '@/components/auth/LoginForm';

export default function Home() {
  return (
    <div 
      className="min-h-screen relative overflow-hidden"
      style={{ 
        background: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%)',
        color: 'white'
      }}
    >
      {/* Animated Background Elements */}
      <div className="absolute inset-0" style={{ zIndex: 0 }}>
        <div 
          className="absolute animate-float"
          style={{ 
            top: '80px', 
            left: '40px', 
            width: '128px', 
            height: '128px', 
            background: 'rgba(147, 51, 234, 0.2)', 
            borderRadius: '50%', 
            filter: 'blur(48px)' 
          }}
        ></div>
        <div 
          className="absolute animate-pulse-slow"
          style={{ 
            top: '160px', 
            right: '80px', 
            width: '96px', 
            height: '96px', 
            background: 'rgba(59, 130, 246, 0.2)', 
            borderRadius: '50%', 
            filter: 'blur(48px)' 
          }}
        ></div>
        <div 
          className="absolute animate-float"
          style={{ 
            bottom: '80px', 
            left: '80px', 
            width: '112px', 
            height: '112px', 
            background: 'rgba(236, 72, 153, 0.2)', 
            borderRadius: '50%', 
            filter: 'blur(48px)',
            animationDelay: '2s' 
          }}
        ></div>
        <div 
          className="absolute animate-pulse-slow"
          style={{ 
            bottom: '160px', 
            right: '40px', 
            width: '80px', 
            height: '80px', 
            background: 'rgba(6, 182, 212, 0.2)', 
            borderRadius: '50%', 
            filter: 'blur(48px)',
            animationDelay: '1s' 
          }}
        ></div>
      </div>

      {/* Main Content Container */}
      <div className="relative container mx-auto" style={{ zIndex: 10, padding: '0 1.5rem' }}>
        {/* Hero Section */}
                {/* Hero Section */}
        <div className="min-h-screen flex items-center">
          <div className="grid w-full items-center lg:grid-cols-[1fr_400px] grid-cols-1 gap-12">
            
            {/* Left Column - Main Content */}
            <div className="flex flex-col gap-8">
              {/* Brand */}
              <div className="flex flex-col gap-4">
                <div className="flex items-center gap-2 mb-6">
                  <div 
                    style={{ 
                      width: '32px', 
                      height: '32px', 
                      background: 'linear-gradient(135deg, #3b82f6, #9333ea)', 
                      borderRadius: '0.5rem',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <span style={{ color: 'white', fontWeight: 'bold', fontSize: '0.875rem' }}>AI</span>
                  </div>
                  <span style={{ color: 'rgba(255, 255, 255, 0.8)', fontWeight: '500' }}>Powered by AI</span>
                </div>
                
                <h1 
                  className="holographic-text text-5xl lg:text-6xl font-bold mb-6 leading-tight"
                >
                  Quote.AI+CRM
                </h1>
                
                <p className="text-xl text-white/90 leading-relaxed mb-4">
                  Générez des devis intelligents en quelques secondes
                </p>
                
                <p className="text-lg text-white/70 leading-relaxed">
                  La seule plateforme CRM qui combine intelligence artificielle et gestion commerciale pour transformer vos visites en devis gagnants automatiquement.
                </p>
              </div>

              {/* Benefits */}
              <div className="flex flex-col gap-3">
                {[
                  'Devis en 30 secondes',
                  '+40% de taux de conversion', 
                  'IA adaptée à votre métier',
                  'CRM intégré complet'
                ].map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-white/90">{benefit}</span>
                  </div>
                ))}
              </div>

              {/* CTAs */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <button className="btn-primary flex items-center justify-center gap-2">
                  Essai gratuit 14 jours
                  <Rocket className="w-5 h-5" />
                </button>
                <button className="btn-secondary flex items-center justify-center gap-2">
                  Voir la démo
                  <ArrowRight className="w-5 h-5" />
                </button>
              </div>

              {/* Trust Signals */}
              <div className="pt-6 text-sm text-white/70">
                <p className="mb-2">Aucune carte bancaire requise</p>
                <p>SSL sécurisé • RGPD conforme • Support 7/7</p>
              </div>
            </div>

            {/* Right Column - Login Form */}
            <div className="flex justify-center lg:justify-end">
              <div className="w-full max-w-md">
                <div className="card-glass p-8">
                  <div className="flex items-center gap-4 mb-6">
                    <div 
                      className="glow-primary w-12 h-12 rounded-full flex items-center justify-center"
                      style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}
                    >
                      <Brain className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">Connexion rapide</h3>
                      <p className="text-white/70 text-sm">Accédez à votre dashboard</p>
                    </div>
                  </div>

                  {/* Social Login */}
                  <div className="flex flex-col gap-3 mb-6">
                    <button className="btn-secondary w-full flex items-center justify-center gap-2">
                      <Sparkles className="w-5 h-5" />
                      <span>Continuer avec Apple</span>
                    </button>
                    <button className="btn-secondary w-full flex items-center justify-center gap-2">
                      <Users className="w-5 h-5" />
                      <span>Continuer avec Google</span>
                    </button>
                  </div>

                  <div className="relative mb-6">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-white/20"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="bg-white/10 backdrop-blur-sm px-2 text-white/70">
                        ou
                      </span>
                    </div>
                  </div>

                  {/* Email Login Form */}
                  <form className="flex flex-col gap-4">
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        className="input-glass w-full pl-12 pr-4 py-3"
                      />
                    </div>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
                      <input 
                        type="password" 
                        placeholder="Mot de passe" 
                        className="input-glass w-full pl-12 pr-4 py-3"
                      />
                    </div>
                    <button type="submit" className="btn-primary w-full">
                      Se connecter
                    </button>
                  </form>

                  <div className="mt-4 text-center">
                    <a 
                      href="#" 
                      className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      Pas encore de compte ? S'inscrire
                    </a>
                  </div>

                  {/* Social Proof */}
                  <div className="mt-6 text-center">
                    <p className="text-white/60 text-sm mb-3">
                      Rejoignez plus de 500+ artisans
                    </p>
                    <div className="flex items-center justify-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-500 fill-yellow-500" />
                      ))}
                      <span className="text-white/80 text-sm ml-2">
                        4.9/5 (127 avis)
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
