'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { CRMService, type Project, type Customer } from '@/lib/crm-service'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Users, 
  FileText,
  Calendar,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle
} from 'lucide-react'
import { formatDate } from '@/lib/utils'

type ProjectWithCustomer = Project & {
  customers: {
    id: string
    name: string
    email: string | null
  }
}

const statusConfig = {
  draft: { label: 'Brouillon', icon: Clock, color: 'text-yellow-400' },
  active: { label: 'Actif', icon: CheckCircle, color: 'text-green-400' },
  completed: { label: 'Terminé', icon: CheckCircle, color: 'text-blue-400' },
  cancelled: { label: 'Annulé', icon: XCircle, color: 'text-red-400' }
}

export default function ProjectsPage() {
  const { user } = useAuth()
  const [projects, setProjects] = useState<ProjectWithCustomer[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingProject, setEditingProject] = useState<ProjectWithCustomer | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [projectsData, customersData] = await Promise.all([
        CRMService.getProjects(),
        CRMService.getCustomers()
      ])
      setProjects(projectsData as ProjectWithCustomer[])
      setCustomers(customersData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.customers.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleAddProject = async (projectData: Omit<Project, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      await CRMService.createProject(projectData)
      setShowAddModal(false)
      loadData()
    } catch (error) {
      console.error('Error creating project:', error)
    }
  }

  const handleUpdateProject = async (id: string, updates: Partial<Project>) => {
    try {
      await CRMService.updateProject(id, updates)
      setEditingProject(null)
      loadData()
    } catch (error) {
      console.error('Error updating project:', error)
    }
  }

  const handleDeleteProject = async (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {
      try {
        await CRMService.deleteProject(id)
        loadData()
      } catch (error) {
        console.error('Error deleting project:', error)
      }
    }
  }

  const getStatusStats = () => {
    const stats = { draft: 0, active: 0, completed: 0, cancelled: 0 }
    projects.forEach(project => {
      stats[project.status as keyof typeof stats]++
    })
    return stats
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="card-glass text-center">
          <div className="w-8 h-8 border-2 border-primary-500/30 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white/70">Chargement des projets...</p>
        </div>
      </div>
    )
  }

  const statusStats = getStatusStats()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="border-b border-white/10">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Projets</h1>
              <p className="text-white/70">Gérez vos projets et devis</p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Nouveau Projet
            </button>
          </div>
        </div>
      </div>

      {/* Search and Stats */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6 mb-8">
          {/* Search and Filters */}
          <div className="flex-1 space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
              <input
                type="text"
                placeholder="Rechercher un projet..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => setStatusFilter('all')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  statusFilter === 'all' 
                    ? 'bg-primary-500 text-white' 
                    : 'bg-white/10 text-white/70 hover:text-white'
                }`}
              >
                Tous ({projects.length})
              </button>
              {Object.entries(statusConfig).map(([status, config]) => (
                <button
                  key={status}
                  onClick={() => setStatusFilter(status)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    statusFilter === status 
                      ? 'bg-primary-500 text-white' 
                      : 'bg-white/10 text-white/70 hover:text-white'
                  }`}
                >
                  {config.label} ({statusStats[status as keyof typeof statusStats]})
                </button>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{projects.length}</div>
              <div className="text-white/70 text-sm">Total Projets</div>
            </div>
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{statusStats.active}</div>
              <div className="text-white/70 text-sm">Actifs</div>
            </div>
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{statusStats.completed}</div>
              <div className="text-white/70 text-sm">Terminés</div>
            </div>
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{statusStats.draft}</div>
              <div className="text-white/70 text-sm">Brouillons</div>
            </div>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => {
            const statusInfo = statusConfig[project.status as keyof typeof statusConfig]
            const StatusIcon = statusInfo.icon
            
            return (
              <div key={project.id} className="card-glass p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setEditingProject(project)}
                      className="text-white/60 hover:text-white"
                      aria-label="Modifier le projet"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteProject(project.id)}
                      className="text-red-400 hover:text-red-300"
                      aria-label="Supprimer le projet"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center gap-2 mb-3">
                  <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                  <span className={`text-sm font-medium ${statusInfo.color}`}>
                    {statusInfo.label}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-white mb-2">{project.name}</h3>
                
                {project.description && (
                  <p className="text-white/70 text-sm mb-4 line-clamp-2">
                    {project.description}
                  </p>
                )}

                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-white/70 text-sm">
                    <Users className="w-4 h-4" />
                    <span>{project.customers.name}</span>
                  </div>
                  {project.customers.email && (
                    <div className="flex items-center gap-2 text-white/60 text-xs">
                      <span>{project.customers.email}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2 text-white/40 text-xs">
                  <Calendar className="w-3 h-3" />
                  <span>Créé le {formatDate(project.created_at)}</span>
                </div>
              </div>
            )
          })}
        </div>

        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-white/20 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchTerm || statusFilter !== 'all' ? 'Aucun projet trouvé' : 'Aucun projet'}
            </h3>
            <p className="text-white/60 mb-6">
              {searchTerm || statusFilter !== 'all'
                ? 'Essayez de modifier vos critères de recherche'
                : 'Commencez par créer votre premier projet'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <button
                onClick={() => setShowAddModal(true)}
                className="btn-primary"
              >
                <Plus className="w-4 h-4 mr-2" />
                Créer un projet
              </button>
            )}
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {(showAddModal || editingProject) && (
        <ProjectModal
          project={editingProject}
          customers={customers}
          onClose={() => {
            setShowAddModal(false)
            setEditingProject(null)
          }}
          onSubmit={editingProject 
            ? (data) => handleUpdateProject(editingProject.id, data)
            : handleAddProject
          }
        />
      )}
    </div>
  )
}

// Project Modal Component
function ProjectModal({ 
  project, 
  customers,
  onClose, 
  onSubmit 
}: { 
  project: ProjectWithCustomer | null
  customers: Customer[]
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
}) {
  const [formData, setFormData] = useState({
    name: project?.name || '',
    description: project?.description || '',
    customer_id: project?.customer_id || '',
    status: project?.status || 'draft'
  })
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      await onSubmit(formData)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="card-glass max-w-md w-full mx-4">
        <h2 className="text-xl font-semibold text-white mb-6">
          {project ? 'Modifier le projet' : 'Nouveau projet'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-white/70 text-sm mb-2">Nom du projet *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
              aria-label="Nom du projet"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Client *</label>
            <select
              value={formData.customer_id}
              onChange={(e) => setFormData({ ...formData, customer_id: e.target.value })}
              required
              aria-label="Sélectionner un client"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Sélectionner un client</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Statut</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as 'draft' | 'active' | 'completed' | 'cancelled' })}
              aria-label="Sélectionner le statut"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="draft">Brouillon</option>
              <option value="active">Actif</option>
              <option value="completed">Terminé</option>
              <option value="cancelled">Annulé</option>
            </select>
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              aria-label="Description du projet"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 btn-secondary"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 btn-primary"
            >
              {loading ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                project ? 'Modifier' : 'Créer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 