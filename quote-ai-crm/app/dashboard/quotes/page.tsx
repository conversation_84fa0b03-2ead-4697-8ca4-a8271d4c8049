'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { CRMService, type Quote, type Project } from '@/lib/crm-service'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  FileText, 
  Euro,
  Calendar,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  Sparkles
} from 'lucide-react'
import { formatDate, formatCurrency } from '@/lib/utils'

type QuoteWithProject = Quote & {
  projects: {
    id: string
    name: string
    customers: {
      id: string
      name: string
      email: string | null
    }
  }
}

const statusConfig = {
  draft: { label: 'Brouillon', icon: Clock, color: 'text-yellow-400' },
  sent: { label: 'Envoyé', icon: Send, color: 'text-blue-400' },
  accepted: { label: 'Accepté', icon: CheckCircle, color: 'text-green-400' },
  rejected: { label: 'Refusé', icon: XCircle, color: 'text-red-400' }
}

export default function QuotesPage() {
  const { user } = useAuth()
  const [quotes, setQuotes] = useState<QuoteWithProject[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingQuote, setEditingQuote] = useState<QuoteWithProject | null>(null)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [quotesData, projectsData] = await Promise.all([
        CRMService.getQuotes(),
        CRMService.getProjects()
      ])
      setQuotes(quotesData as QuoteWithProject[])
      setProjects(projectsData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quote.projects.customers.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || quote.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const handleAddQuote = async (quoteData: Omit<Quote, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      await CRMService.createQuote(quoteData)
      setShowAddModal(false)
      loadData()
    } catch (error) {
      console.error('Error creating quote:', error)
    }
  }

  const handleUpdateQuote = async (id: string, updates: Partial<Quote>) => {
    try {
      await CRMService.updateQuote(id, updates)
      setEditingQuote(null)
      loadData()
    } catch (error) {
      console.error('Error updating quote:', error)
    }
  }

  const handleDeleteQuote = async (id: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce devis ?')) {
      try {
        await CRMService.deleteQuote(id)
        loadData()
      } catch (error) {
        console.error('Error deleting quote:', error)
      }
    }
  }

  const getStatusStats = () => {
    const stats = { draft: 0, sent: 0, accepted: 0, rejected: 0 }
    quotes.forEach(quote => {
      stats[quote.status as keyof typeof stats]++
    })
    return stats
  }

  const getTotalRevenue = () => {
    return quotes
      .filter(quote => quote.status === 'accepted')
      .reduce((sum, quote) => sum + quote.total_amount, 0)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="card-glass text-center">
          <div className="w-8 h-8 border-2 border-primary-500/30 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white/70">Chargement des devis...</p>
        </div>
      </div>
    )
  }

  const statusStats = getStatusStats()
  const totalRevenue = getTotalRevenue()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <div className="border-b border-white/10">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Devis</h1>
              <p className="text-white/70">Générez et gérez vos devis intelligents</p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="btn-primary flex items-center gap-2"
            >
              <Sparkles className="w-4 h-4" />
              Nouveau Devis
            </button>
          </div>
        </div>
      </div>

      {/* Search and Stats */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-6 mb-8">
          {/* Search and Filters */}
          <div className="flex-1 space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
              <input
                type="text"
                placeholder="Rechercher un devis..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg pl-10 pr-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => setStatusFilter('all')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  statusFilter === 'all' 
                    ? 'bg-primary-500 text-white' 
                    : 'bg-white/10 text-white/70 hover:text-white'
                }`}
              >
                Tous ({quotes.length})
              </button>
              {Object.entries(statusConfig).map(([status, config]) => (
                <button
                  key={status}
                  onClick={() => setStatusFilter(status)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    statusFilter === status 
                      ? 'bg-primary-500 text-white' 
                      : 'bg-white/10 text-white/70 hover:text-white'
                  }`}
                >
                  {config.label} ({statusStats[status as keyof typeof statusStats]})
                </button>
              ))}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{quotes.length}</div>
              <div className="text-white/70 text-sm">Total Devis</div>
            </div>
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{statusStats.accepted}</div>
              <div className="text-white/70 text-sm">Acceptés</div>
            </div>
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{statusStats.sent}</div>
              <div className="text-white/70 text-sm">Envoyés</div>
            </div>
            <div className="card-glass text-center px-4 py-4">
              <div className="text-2xl font-bold holographic-text">{formatCurrency(totalRevenue)}</div>
              <div className="text-white/70 text-sm">CA Total</div>
            </div>
          </div>
        </div>

        {/* Quotes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredQuotes.map((quote) => {
            const statusInfo = statusConfig[quote.status as keyof typeof statusConfig]
            const StatusIcon = statusInfo.icon
            
            return (
              <div key={quote.id} className="card-glass p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-accent-500 to-primary-500 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setEditingQuote(quote)}
                      className="text-white/60 hover:text-white"
                      aria-label="Modifier le devis"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteQuote(quote.id)}
                      className="text-red-400 hover:text-red-300"
                      aria-label="Supprimer le devis"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center gap-2 mb-3">
                  <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                  <span className={`text-sm font-medium ${statusInfo.color}`}>
                    {statusInfo.label}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-white mb-2">{quote.title}</h3>
                
                {quote.description && (
                  <p className="text-white/70 text-sm mb-4 line-clamp-2">
                    {quote.description}
                  </p>
                )}

                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-white/70 text-sm">
                    <FileText className="w-4 h-4" />
                    <span>{quote.projects.name}</span>
                  </div>
                  <div className="flex items-center gap-2 text-white/60 text-xs">
                    <span>{quote.projects.customers.name}</span>
                  </div>
                  <div className="flex items-center gap-2 text-white/90 font-semibold">
                    <Euro className="w-4 h-4" />
                    <span>{formatCurrency(quote.total_amount)}</span>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-white/40 text-xs">
                  <Calendar className="w-3 h-3" />
                  <span>Créé le {formatDate(quote.created_at)}</span>
                </div>
              </div>
            )
          })}
        </div>

        {filteredQuotes.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-white/20 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              {searchTerm || statusFilter !== 'all' ? 'Aucun devis trouvé' : 'Aucun devis'}
            </h3>
            <p className="text-white/60 mb-6">
              {searchTerm || statusFilter !== 'all'
                ? 'Essayez de modifier vos critères de recherche'
                : 'Commencez par créer votre premier devis'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <button
                onClick={() => setShowAddModal(true)}
                className="btn-primary"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Créer un devis
              </button>
            )}
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {(showAddModal || editingQuote) && (
        <QuoteModal
          quote={editingQuote}
          projects={projects}
          onClose={() => {
            setShowAddModal(false)
            setEditingQuote(null)
          }}
          onSubmit={editingQuote 
            ? (data) => handleUpdateQuote(editingQuote.id, data)
            : handleAddQuote
          }
        />
      )}
    </div>
  )
}

// Quote Modal Component
function QuoteModal({ 
  quote, 
  projects,
  onClose, 
  onSubmit 
}: { 
  quote: QuoteWithProject | null
  projects: Project[]
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
}) {
  const [formData, setFormData] = useState({
    title: quote?.title || '',
    description: quote?.description || '',
    project_id: quote?.project_id || '',
    total_amount: quote?.total_amount || 0,
    status: quote?.status || 'draft',
    valid_until: quote?.valid_until || ''
  })
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      await onSubmit(formData)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="card-glass max-w-md w-full mx-4">
        <h2 className="text-xl font-semibold text-white mb-6">
          {quote ? 'Modifier le devis' : 'Nouveau devis'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-white/70 text-sm mb-2">Titre du devis *</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
              aria-label="Titre du devis"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Projet *</label>
            <select
              value={formData.project_id}
              onChange={(e) => setFormData({ ...formData, project_id: e.target.value })}
              required
              aria-label="Sélectionner un projet"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Sélectionner un projet</option>
              {projects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Montant total *</label>
            <input
              type="number"
              step="0.01"
              value={formData.total_amount}
              onChange={(e) => setFormData({ ...formData, total_amount: parseFloat(e.target.value) || 0 })}
              required
              aria-label="Montant total"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Statut</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as 'draft' | 'sent' | 'accepted' | 'rejected' })}
              aria-label="Sélectionner le statut"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="draft">Brouillon</option>
              <option value="sent">Envoyé</option>
              <option value="accepted">Accepté</option>
              <option value="rejected">Refusé</option>
            </select>
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Date de validité</label>
            <input
              type="date"
              value={formData.valid_until}
              onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
              aria-label="Date de validité"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div>
            <label className="block text-white/70 text-sm mb-2">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              aria-label="Description du devis"
              className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 btn-secondary"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 btn-primary"
            >
              {loading ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                quote ? 'Modifier' : 'Créer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 