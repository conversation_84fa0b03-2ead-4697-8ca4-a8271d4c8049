'use client'

import { useAuth } from '@/contexts/AuthContext'
import { redirect } from 'next/navigation'
import { useEffect } from 'react'
import { 
  Sparkles, 
  Users, 
  FileText, 
  TrendingUp, 
  Settings, 
  LogOut,
  Plus,
  Search,
  Bell
} from 'lucide-react'

export default function Dashboard() {
  const { user, loading, signOut } = useAuth()

  useEffect(() => {
    if (!loading && !user) {
      redirect('/')
    }
  }, [user, loading])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="card-glass text-center">
          <div className="w-8 h-8 border-2 border-primary-500/30 border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white/70">Chargement...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold holographic-text">Quote.AI+CRM</h1>
              <div className="flex items-center gap-2 text-white/60">
                <Sparkles className="w-4 h-4" />
                <span className="text-sm">Powered by AI</span>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <button className="btn-secondary" aria-label="Notifications">
                <Bell className="w-4 h-4" />
              </button>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                <div className="text-white">
                  <div className="text-sm font-medium">{user.email}</div>
                  <div className="text-xs text-white/60">En ligne</div>
                </div>
                <button 
                  onClick={signOut}
                  className="text-white/60 hover:text-white"
                  aria-label="Se déconnecter"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <aside className="lg:col-span-1">
            <nav className="space-y-2">
              <a href="#" className="flex items-center gap-3 p-3 rounded-lg bg-white/10 text-white">
                <TrendingUp className="w-5 h-5" />
                <span>Dashboard</span>
              </a>
              <a href="/dashboard/customers" className="flex items-center gap-3 p-3 rounded-lg text-white/70 hover:text-white hover:bg-white/5">
                <Users className="w-5 h-5" />
                <span>Clients</span>
              </a>
              <a href="/dashboard/projects" className="flex items-center gap-3 p-3 rounded-lg text-white/70 hover:text-white hover:bg-white/5">
                <FileText className="w-5 h-5" />
                <span>Projets</span>
              </a>
              <a href="#" className="flex items-center gap-3 p-3 rounded-lg text-white/70 hover:text-white hover:bg-white/5">
                <Sparkles className="w-5 h-5" />
                <span>AI Assistant</span>
              </a>
              <a href="#" className="flex items-center gap-3 p-3 rounded-lg text-white/70 hover:text-white hover:bg-white/5">
                <Settings className="w-5 h-5" />
                <span>Paramètres</span>
              </a>
            </nav>
          </aside>

          {/* Main Content */}
          <main className="lg:col-span-3">
            {/* Welcome Section */}
            <div className="card-glass mb-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">Bienvenue, {user.email} !</h2>
                  <p className="text-white/70">Quote.AI est prêt à vous aider</p>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="card-holographic text-center p-6">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Nouveau Devis</h3>
                <p className="text-white/70 text-sm mb-4">Créer un devis intelligent</p>
                <button className="btn-primary w-full">Créer</button>
              </div>

              <div className="card-holographic text-center p-6">
                <div className="w-12 h-12 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Nouveau Client</h3>
                <p className="text-white/70 text-sm mb-4">Ajouter un client</p>
                <button className="btn-primary w-full">Ajouter</button>
              </div>

              <div className="card-holographic text-center p-6">
                <div className="w-12 h-12 bg-gradient-to-r from-accent-500 to-primary-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">AI Assistant</h3>
                <p className="text-white/70 text-sm mb-4">Demander de l'aide</p>
                <button className="btn-primary w-full">Demander</button>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <div className="card-glass text-center p-6">
                <div className="text-2xl font-bold holographic-text mb-2">12</div>
                <div className="text-white/70 text-sm">Clients actifs</div>
              </div>
              <div className="card-glass text-center p-6">
                <div className="text-2xl font-bold holographic-text mb-2">8</div>
                <div className="text-white/70 text-sm">Devis ce mois</div>
              </div>
              <div className="card-glass text-center p-6">
                <div className="text-2xl font-bold holographic-text mb-2">€24,500</div>
                <div className="text-white/70 text-sm">Chiffre d'affaires</div>
              </div>
              <div className="card-glass text-center p-6">
                <div className="text-2xl font-bold holographic-text mb-2">85%</div>
                <div className="text-white/70 text-sm">Taux de conversion</div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="card-glass">
              <h3 className="text-lg font-semibold text-white mb-6">Activité récente</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 bg-white/5 rounded-lg">
                  <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="text-white font-medium">Nouveau devis créé</div>
                    <div className="text-white/60 text-sm">Devis #Q-2024-001 pour Client ABC</div>
                  </div>
                  <div className="text-white/40 text-sm">Il y a 2h</div>
                </div>
                <div className="flex items-center gap-4 p-4 bg-white/5 rounded-lg">
                  <div className="w-10 h-10 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="text-white font-medium">Client ajouté</div>
                    <div className="text-white/60 text-sm">Nouveau client: Entreprise XYZ</div>
                  </div>
                  <div className="text-white/40 text-sm">Il y a 4h</div>
                </div>
                <div className="flex items-center gap-4 p-4 bg-white/5 rounded-lg">
                  <div className="w-10 h-10 bg-gradient-to-r from-accent-500 to-primary-500 rounded-full"></div>
                  <div className="flex-1">
                    <div className="text-white font-medium">Devis accepté</div>
                    <div className="text-white/60 text-sm">Devis #Q-2024-002 accepté par Client DEF</div>
                  </div>
                  <div className="text-white/40 text-sm">Il y a 1j</div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
} 