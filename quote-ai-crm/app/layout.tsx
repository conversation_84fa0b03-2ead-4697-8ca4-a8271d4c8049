import type { Metadata, Viewport } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

export const metadata: Metadata = {
  title: "Quote.AI+CRM - Advanced SaaS CRM with AI-powered quote generation",
  description: "Generate intelligent quotes, manage customers, and automate your business with AI-powered CRM",
  keywords: "CRM, AI, quotes, business automation, SaaS",
  authors: [{ name: "Quote.AI+CRM Team" }],
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className="dark">
      <body className="antialiased">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
