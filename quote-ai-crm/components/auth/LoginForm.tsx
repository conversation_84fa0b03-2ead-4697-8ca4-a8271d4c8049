'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { ArrowRight, Mail, Lock, User, Apple, Chrome } from 'lucide-react'

export function LoginForm() {
  const { signIn, signUp, signInWithGoogle, signInWithApple } = useAuth()
  const [isLogin, setIsLogin] = useState(true)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      if (isLogin) {
        await signIn(email, password)
      } else {
        await signUp(email, password, fullName)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue')
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setLoading(true)
    try {
      await signInWithGoogle()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue')
    } finally {
      setLoading(false)
    }
  }

  const handleAppleSignIn = async () => {
    setLoading(true)
    try {
      await signInWithApple()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="card-glass max-w-md mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-white mb-2">
          {isLogin ? 'Content de vous revoir !' : 'Créer un compte'}
        </h2>
        <p className="text-white/70">
          {isLogin 
            ? 'Connectez-vous pour continuer' 
            : 'Rejoignez Quote.AI+CRM gratuitement'
          }
        </p>
      </div>

      {/* Social Login Buttons */}
      <div className="space-y-3 mb-6">
        <button
          onClick={handleAppleSignIn}
          disabled={loading}
          className="w-full btn-secondary flex items-center justify-center gap-3"
        >
          <Apple className="w-5 h-5" />
          Continuer avec Apple
        </button>
        
        <button
          onClick={handleGoogleSignIn}
          disabled={loading}
          className="w-full btn-secondary flex items-center justify-center gap-3"
        >
          <Chrome className="w-5 h-5" />
          Continuer avec Google
        </button>
      </div>

      <div className="relative mb-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-white/20" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 text-white/60" style={{ 
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)'
          }}>ou</span>
        </div>
      </div>

      {/* Email/Password Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {!isLogin && (
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
            <input
              type="text"
              placeholder="Nom complet"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              required={!isLogin}
              className="input-glass w-full pl-10 pr-4 py-3"
            />
          </div>
        )}

        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="input-glass w-full pl-10 pr-4 py-3"
          />
        </div>

        <div className="relative">
          <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
          <input
            type="password"
            placeholder="Mot de passe"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="input-glass w-full pl-10 pr-4 py-3"
          />
        </div>

        {error && (
          <div className="text-red-400 text-sm text-center p-3 bg-red-500/10 rounded-lg">
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={loading}
          className="w-full btn-primary flex items-center justify-center gap-2"
        >
          {loading ? (
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          ) : (
            <ArrowRight className="w-5 h-5" />
          )}
          {isLogin ? 'Se connecter' : 'Créer un compte'}
        </button>
      </form>

      <div className="text-center mt-6">
        <button
          onClick={() => setIsLogin(!isLogin)}
          className="text-white/60 hover:text-white text-sm transition-colors"
          style={{ color: '#667eea' }}
        >
          {isLogin 
            ? "Pas encore de compte ? S'inscrire" 
            : "Déjà un compte ? Se connecter"
          }
        </button>
      </div>
    </div>
  )
} 