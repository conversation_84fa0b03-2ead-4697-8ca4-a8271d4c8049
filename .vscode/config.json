{"projectName": "Quote.AI+CRM", "description": "Advanced SaaS CRM with AI-powered quote generation and property management", "version": "1.0.0", "framework": "Next.js + TypeScript + Tailwind CSS", "database": "Supabase", "ai": {"primaryModels": ["openai/gpt-oss-20b", "deepseek-coder-v2-lite-instruct"], "usage": "Coding assistance, debugging, architecture suggestions, and SaaS feature ideation"}, "deployment": "Vercel", "currentStatus": {"workingProject": "quote-ai-crm", "server": "✅ Working on localhost:3000", "landingPage": "🔄 Need to fix routing to display main page", "authentication": "🔄 LoginForm component ready", "dashboard": "🔄 Basic structure in place", "database": "🔄 Supabase configuration ready", "issues": ["Main page (/) not loading correctly - shows wrong content", "Need to ensure proper app/page.tsx routing", "Test page (/test) works - server is functional"], "nextSteps": ["Fix main page routing and display", "Complete authentication integration", "Connect and test Supabase database", "Implement full dashboard functionality"]}, "rules": {"alwaysApply": true, "codingStyle": {"language": "TypeScript", "framework": "React/Next.js", "styling": "Tailwind CSS with holographic design system", "stateManagement": "React Context + Supabase", "aiIntegration": "Primary: gpt-oss-20b for reasoning, DeepSeek Coder for coding"}, "developmentWorkflow": {"approach": "Step-by-step implementation", "testing": "Test each step thoroughly before proceeding", "documentation": "Update README after each phase", "qualityGates": "Working milestone for each phase"}, "aiUsage": {"primary": "gpt-oss-20b for architecture & reasoning, DeepSeek Coder for code generation & debugging", "approach": "Ask questions and get suggestions before implementing", "implementation": "Only implement when certain it's correct", "scope": "SaaS Landing, Register/Login, CRM + AI automation"}, "designSystem": {"theme": "Holographic gradients with glassmorphism", "colors": "Use projectkleuren.mdc color palette", "components": "Modern, responsive, dark-mode first", "animations": "Smooth transitions and holographic effects"}, "architecture": {"frontend": "Next.js with TypeScript", "backend": "Supabase (PostgreSQL + Auth + Storage)", "ai": "Integration with gpt-oss-20b and DeepSeek Coder", "deployment": "Vercel with domain configuration"}}, "phases": {"current": "FASE 1 - FOUNDATION", "currentStep": "1.4 - <PERSON> Complete", "completion": "75%", "nextAction": "Implement authentication and dashboard functionality"}, "features": {"core": ["User Authentication", "Company Management", "Customer Management", "Project Management", "Quote Generation", "Invoice Management"], "ai": ["AI Quote Assistant", "Smart Pricing", "Lead Scoring", "Automated Workflows"], "analytics": ["Dashboard Analytics", "Sales Pipeline", "Revenue Tracking", "Performance Insights"]}, "fileStructure": {"app": {"description": "Next.js App Router structure", "dashboard": {"description": "Dashboard routes", "customers": "Customer management pages", "projects": "Project management pages", "quotes": "Quote management pages", "page.tsx": "Dashboard home"}, "favicon.ico": "Favicon for the app", "globals.css": "Global styles with holographic effects", "layout.tsx": "Root layout with providers", "page.tsx": "Landing page with holographic design"}, "components": {"auth": "Authentication components (LoginForm, etc)", "dashboard": "Dashboard-specific components", "ui": "Reusable UI components"}, "contexts": "React contexts for state management", "hooks": "Custom React hooks", "lib": {"ai-service.ts": "AI integration service", "crm-service.ts": "CRM business logic", "supabase.ts": "Database configuration", "utils.ts": "Utility functions"}, "public": "Static assets and images", "types": "TypeScript type definitions", "supabase": {"migrations": "Database schema migrations (001_initial_schema.sql, 002_rls_policies.sql)"}}, "vscode": {"config": "Opgeschoonde configuratie"}, "quote-ai-crm": {"app": "Landing + Dashboard", "components": "Auth + UI components", "lib": "Services + utilities", "supabase/migrations": "Database schema", "nextJsConfig": "Volledig geconfigureerd"}, "venv": "Python environment (behouden)"}