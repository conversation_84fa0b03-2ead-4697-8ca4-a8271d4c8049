{"server": {"host": "0.0.0.0", "port": 1235, "cors_enabled": true, "api_key_required": false, "max_requests_per_minute": 60, "enable_gpu": true, "max_context_length": 32768}, "models": {"gpt-oss-20b": {"model_name": "openai/gpt-oss-20b", "context_length": 32768, "max_tokens": 4096, "temperature": 0.3, "top_p": 0.9, "specialization": ["reasoning", "architecture", "complex-planning", "system-design"]}, "deepseek-coder-v2-lite": {"model_name": "deepseek-coder-v2-lite-instruct", "context_length": 16384, "max_tokens": 2048, "temperature": 0.15, "top_p": 0.9, "specialization": ["code", "debugging", "frontend", "backend", "quick-fixes"]}}}