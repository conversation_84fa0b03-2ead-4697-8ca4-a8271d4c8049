import request from 'supertest';
import { createServer } from 'http';
import { SimpleServer } from '../src/main';

describe('SimpleServer', () => {
  let server: SimpleServer;

  beforeAll(async () => {
    server = new SimpleServer({ port: 3001 });
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  describe('GET /', () => {
    it('should return HTML welcome page', async () => {
      const response = await request('http://localhost:3001')
        .get('/')
        .expect(200)
        .expect('Content-Type', /html/);

      expect(response.text).toContain('Welcome to TypeScript Server!');
    });
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request('http://localhost:3001')
        .get('/health')
        .expect(200)
        .expect('Content-Type', /json/);

      expect(response.body).toHaveProperty('status', 'OK');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('GET /api/status', () => {
    it('should return API status', async () => {
      const response = await request('http://localhost:3001')
        .get('/api/status')
        .expect(200)
        .expect('Content-Type', /json/);

      expect(response.body).toHaveProperty('api', 'Simple TypeScript Server');
      expect(response.body).toHaveProperty('version', '1.0.0');
      expect(response.body).toHaveProperty('status', 'active');
      expect(response.body).toHaveProperty('endpoints');
    });
  });

  describe('GET /nonexistent', () => {
    it('should return 404 for unknown routes', async () => {
      const response = await request('http://localhost:3001')
        .get('/nonexistent')
        .expect(404)
        .expect('Content-Type', /json/);

      expect(response.body).toHaveProperty('error', 'Not Found');
      expect(response.body).toHaveProperty('statusCode', 404);
    });
  });
});
